-- core/bot.lua
-- Bot核心类：OneBot协议机器人框架的主入口
-- 工作流程：
-- 1. 实例化Bot - 创建dispatcher、server、混入API方法
-- 2. 注册回调 - 通过dispatcher管理事件监听器
-- 3. 注册WebSocket - 创建WS服务器处理OneBot通信
-- 4. 注册API - 将ob11_api的所有方法混入Bot实例
-- 5. 加载插件 - 动态加载plugins目录下的Lua插件
-- 6. 启动服务 - 开始监听WebSocket连接，进入事件循环
-- 这是一个事件驱动的松耦合架构，插件通过事件系统与框架交互

local Dispatcher = require('./dispatcher')
local Server = require('./ws-server')
local ob11_api = require('./ob11-api')
local fs = require('fs')

local Bot = {}
Bot.__index = Bot

function Bot:new(config, log)
    local self = setmetatable({}, Bot)
    self.config = config
    self.log = log
    self.dispatcher = Dispatcher:new()
    self.server = Server:new(config.ws_server, self.dispatcher, self.log)
    self.api_call_id = 0

    -- 将所有 API 方法添加到 Bot 实例
    for name, func in pairs(ob11_api) do
        if type(func) == 'function' then
            self[name] = func
        end
    end

    self.send = function(action_payload)
        return self.server:send(action_payload)
    end

    return self
end

-- 提供给插件注册事件的接口
function Bot:on(event_name, callback)
    self.dispatcher:on(event_name, callback)
end

-- 加载插件
function Bot:load_plugins(dir)
    local ok, files_or_err = pcall(fs.readdirSync, dir)

    if not ok then
        self.log:error(string.format("读取插件目录失败: '%s': %s", dir, tostring(files_or_err)))
        return
    end

    local files = files_or_err
    for _, file in ipairs(files) do
        if file:match('%.lua$') then
            self.log:info(string.format("加载插件: %s", file))
            local plugin_func, err = loadfile(dir .. "/" .. file)
            if plugin_func then
                pcall(plugin_func(), self)
            else
                self.log:error(string.format("加载插件失败: '%s': %s", file, err))
            end
        end
    end
end

-- 启动 Bot
function Bot:run()
    self.server:start()
end

return Bot



