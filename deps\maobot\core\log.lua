local LogManager = {}
LogManager.__index = LogManager

function LogManager:new(config)
    local self = setmetatable({}, LogManager)
    self.logger = require('logger')
    self.config = config or {
        showFullPath = false,
        customPrefix = "MaoBot"
    }
    
    -- 包装logger方法
    for _, level in ipairs({ "info", "warn", "error", "debug", "trace" }) do
        self[level] = function(_, msg, debugInfo)
            local override = {
                prefix = self.config.customPrefix,
                noLineInfo = not self.config.showFullPath and function()
                    local info = debugInfo or debug.getinfo(3, "Sl")
                    local src = info.source:gsub("^@", "")
                    local filename = src:match("([^/\\]+)$") or src
                    return filename:gsub("%.lua$", "") .. ":" .. info.currentline
                end or nil
            }
            return self.logger[level](msg, debugInfo, override)
        end
    end
    
    return self
end

function LogManager:setConfig(newConfig)
    for k, v in pairs(newConfig) do
        self.config[k] = v
    end
end

return LogManager
