-- core/message_processor.lua
-- 消息预处理模块：处理消息解析、@检测、昵称匹配等

local MessageProcessor = {}

-- 解析消息段 - 直接使用OneBot事件中的message字段
local function parseMessage(event)
    -- OneBot事件中已经包含解析好的message字段
    return event.message or {}
end

-- 预处理消息事件
function MessageProcessor.preprocessMessageEvent(event, log)
    local msgs = parseMessage(event)

    if #msgs > 0 then
        local filtered = {}
        for i, msg in ipairs(msgs) do
            if i < #msgs and msg.type == "at" and msgs[i + 1].type == "text" then
                msgs[i + 1].data.text = msgs[i + 1].data.text:gsub("^%s+", "")
            end
            if msg.type ~= "text" or (msg.data.text and msg.data.text ~= "") then
                table.insert(filtered, msg)
            end
        end
        event.message = filtered
    else
        event.message = {}
    end

    if event.message_type == "group" then
        log:info(string.format("收到群(%s)消息 %s : %s",
            event.group_id, event.sender.nickname or event.user_id, event.raw_message))
    elseif event.message_type == "guild" and event.sub_type == "channel" then
        log:info(string.format("收到频道(%s)(%s-%s)消息 %s : %s",
            event.group_id, event.guild_id, event.channel_id,
            event.sender.nickname or event.user_id, event.raw_message))
    else
        log:info(string.format("收到私聊消息 %s : %s",
            event.sender.nickname or event.user_id, event.raw_message))
    end

    if #event.message > 0 and event.message[1].type == "text" then
        event.message[1].data.text = event.message[1].data.text:gsub("^%s+", "")
    end
end

return MessageProcessor




