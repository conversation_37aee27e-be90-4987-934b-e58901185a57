-- core/Ws.lua
-- WebSocket服务器：负责与OneBot客户端的通信
-- 工作原理：
-- 1. 启动WebSocket服务器监听指定端口
-- 2. 接收OneBot发来的事件消息并解析JSON
-- 3. 构建事件名称并通过dispatcher分发给插件
-- 4. 提供API调用接口，将插件的响应发送回OneBot
-- 实现了OneBot协议的服务端，是机器人框架的通信核心

-- 引入WebSocket 服务器模块
local CustomWsServer = require('websocket')
local MessageProcessor = require('./message')
local json = require('json')

local Server = {}
Server.__index = Server

function Server:new(config, dispatcher, log)
    local self = setmetatable({}, Server)
    self.config = config
    self.dispatcher = dispatcher
    self.connection = nil
    self.log = log
    return self
end

function Server:start()
    -- 使用您的自定义服务器来创建实例
    local ws_server = CustomWsServer.server.new()

    -- 监听您服务器的 'connect' 事件
    ws_server:on('connect', function(client)
        self.log:info("OneBot客户端通过 WebSocket服务器 连接成功")
        self.connection = client
    end)

    -- 监听您服务器的 'data' 事件
    ws_server:on('data', function(client, message)
        local ok, event = pcall(json.decode, message)
        if not ok or type(event) ~= 'table' then
            self.log:error(string.format("[Ws-Server] 错误解析JSON: %s", message))
            return
        end

        -- 如果是消息事件，进行预处理
        if event.post_type == "message" then
            MessageProcessor.preprocessMessageEvent(event, self.log)
        end

        -- 构建事件名称并分发
        local event_name = event.post_type
        if event.message_type then
            event_name = event_name .. "." .. event.message_type
        elseif event.notice_type then
            event_name = event_name .. "." .. event.notice_type
        elseif event.request_type then
            event_name = event_name .. "." .. event.request_type
        end

        self.dispatcher:emit(event_name, event)
        self.dispatcher:emit('*', event)
    end)

    -- 监听您服务器的 'disconnect' 事件
    ws_server:on('disconnect', function(client)
        self.log:error("OneBot客户端通过 WebSocket服务器 连接断开")
        self.connection = nil
    end)

    -- 启动服务器监听
    ws_server:listen(self.config.port, self.config.host)
    self.log:info(string.format("WebSocket服务器启动成功, 监听 ws://%s:%d", self.config.host, self.config.port))
end

-- 发送 API 调用的函数也需要适配
function Server:send(action_payload)
    if self.connection then
        local ok, json_str = pcall(json.encode, action_payload)
        if ok then
            -- 调用您在 client 对象上附加的 send 方法
            self.connection:send(json_str)
        else
            self.log:error(string.format("[Ws-Server] 解析JSON错误: %s", action_payload))
        end
    else
        self.log:error("没有活动连接，不能发送API调用")
    end
end

return Server
