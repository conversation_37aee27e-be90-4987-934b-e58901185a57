
-- MaoBot框架入口
local MaoBot = require('./core/bot')
local LogManager = require('./core/log')

local M = {}

function M.create(config)
    local log = LogManager:new(config.log)
    return MaoBot:new(config, log)
end

-- 兼容直接运行
function M.run(config_path)
    config_path = config_path or './config'
    local config = require(config_path)
    local bot = M.create(config)
    bot:load_plugins('plugins')
    bot:run()
end

return M

